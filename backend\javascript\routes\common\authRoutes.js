// node_gemini_summarizer/routes/authRoutes.js
import express from 'express';
import {
    register,
    login,
    verifyEmail,
    resendVerificationCode,
    getCurrentUser
} from '../../controllers/common/auth/index.js';
import { protect } from '../../middleware/authMiddleware.js';

const router = express.Router();

// User Routes
router.post('/register', register);
router.post('/login', login);
router.post('/verify-email', verifyEmail);
router.post('/resend-verification', resendVerificationCode);
router.get('/me', protect, getCurrentUser);

export default router;